<script lang="ts">
  import { userStore } from '$lib/stores';
  import GameCanvas from '$lib/components/GameCanvas.svelte';
  import TaskForm from '$lib/components/TaskForm.svelte';
  import TaskList from '$lib/components/TaskList.svelte';


</script>

<main class="container">
  <header class="header">
    <h1>🍌 Banana Checklist</h1>
    <div class="banana-counter">
      <span class="banana-icon">🍌</span>
      <span class="banana-count">{$userStore.bananaCount}</span>
    </div>
  </header>

  <div class="app-layout">
    <section class="task-section">
      <TaskForm />
      <TaskList />
    </section>

    <section class="game-section">
      <h2>🎮 Monkey Adventure</h2>
      <GameCanvas />
    </section>
  </div>

  <footer class="footer">
    <p>Complete tasks to earn bananas and unlock new features!</p>
  </footer>
</main>

<style>
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1rem;
    background: linear-gradient(135deg, #10B981, #059669);
    color: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }

  .header h1 {
    margin: 0;
    font-size: 2rem;
    font-weight: bold;
  }

  .banana-counter {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 1.2rem;
    font-weight: bold;
  }

  .banana-icon {
    font-size: 1.5rem;
  }

  .app-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
  }

  .task-section, .game-section {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: 2px solid #E5E7EB;
  }

  .game-section h2 {
    margin-top: 0;
    color: #374151;
    border-bottom: 2px solid #10B981;
    padding-bottom: 0.5rem;
  }



  .footer {
    text-align: center;
    padding: 1rem;
    color: #6B7280;
    font-style: italic;
  }



  @media (max-width: 768px) {
    .app-layout {
      grid-template-columns: 1fr;
    }

    .header {
      flex-direction: column;
      gap: 1rem;
    }

    .header h1 {
      font-size: 1.5rem;
    }
  }
</style>
