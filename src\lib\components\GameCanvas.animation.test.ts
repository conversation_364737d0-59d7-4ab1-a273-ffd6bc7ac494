import { describe, it, expect, beforeEach, vi } from 'vitest';

// Mock PixiJS for animation testing
vi.mock('pixi.js', () => {
    return {
        Application: vi.fn(() => ({
            stage: {
                addChild: vi.fn(),
                removeChild: vi.fn(),
            },
        })),
        AnimatedSprite: vi.fn(() => ({
            scale: { x: 2, y: 2, set: vi.fn() },
            x: 100,
            y: 400,
            animationSpeed: 0.15,
            loop: true,
            playing: false,
            play: vi.fn(),
            stop: vi.fn(),
            gotoAndStop: vi.fn(),
        })),
        Assets: {
            load: vi.fn().mockResolvedValue({ source: { width: 576, height: 32 } }),
        },
        Texture: vi.fn(() => ({ source: { width: 576, height: 32 } })),
        Rectangle: vi.fn(),
    };
});

describe('GameCanvas Animation System', () => {
    let switchMonkeyAnimation: (type: 'idle' | 'run' | 'jump') => void;
    let monkey: any;
    let idleFrames: any[];
    let runFrames: any[];
    let jumpFrames: any[];

    beforeEach(async () => {
        vi.clearAllMocks();

        // Setup mock frames
        const mockTexture = { source: { width: 576, height: 32 } };
        idleFrames = Array(18).fill(mockTexture);
        runFrames = Array(8).fill(mockTexture);
        jumpFrames = Array(4).fill(mockTexture);

        // Import PIXI to get the mocked version
        const PIXI = await import('pixi.js');

        // Setup mock monkey with proper spies
        monkey = {
            scale: { x: 2, y: 2, set: vi.fn() },
            x: 100,
            y: 400,
            animationSpeed: 0.15,
            loop: true,
            playing: false,
            play: vi.fn(),
            stop: vi.fn(),
            gotoAndStop: vi.fn(),
        };

        // Mock the switchMonkeyAnimation function
        switchMonkeyAnimation = (animationType: 'idle' | 'run' | 'jump') => {
            if (!monkey || !idleFrames.length || !runFrames.length || !jumpFrames.length) return;

            const currentScale = { x: monkey.scale.x, y: monkey.scale.y };
            const currentPosition = { x: monkey.x, y: monkey.y };

            // Create new sprite with appropriate frames
            let animationSpeed: number;

            switch (animationType) {
                case 'idle':
                    animationSpeed = 0.15;
                    break;
                case 'run':
                    animationSpeed = 0.2;
                    break;
                case 'jump':
                    animationSpeed = 0.25;
                    break;
                default:
                    animationSpeed = 0.15;
            }

            // Update monkey properties
            monkey.animationSpeed = animationSpeed;
            monkey.loop = true;
            monkey.scale.set(currentScale.x, currentScale.y);
            monkey.x = currentPosition.x;
            monkey.y = currentPosition.y;
            monkey.play();
        };
    });

    describe('Animation Switching', () => {
        it('should switch from idle to run animation', () => {
            // Start with idle
            switchMonkeyAnimation('idle');
            expect(monkey.animationSpeed).toBe(0.15);
            expect(monkey.play).toHaveBeenCalled();

            // Switch to run
            vi.clearAllMocks();
            switchMonkeyAnimation('run');
            expect(monkey.animationSpeed).toBe(0.2);
            expect(monkey.scale.set).toHaveBeenCalledWith(2, 2);
        });

        it('should switch from run to jump animation', () => {
            // Start with run
            switchMonkeyAnimation('run');
            expect(monkey.animationSpeed).toBe(0.2);

            // Switch to jump
            vi.clearAllMocks();
            switchMonkeyAnimation('jump');
            expect(monkey.animationSpeed).toBe(0.25);
            expect(monkey.scale.set).toHaveBeenCalledWith(2, 2);
        });

        it('should switch from jump back to idle animation', () => {
            // Start with jump
            switchMonkeyAnimation('jump');
            expect(monkey.animationSpeed).toBe(0.25);

            // Switch back to idle
            vi.clearAllMocks();
            switchMonkeyAnimation('idle');
            expect(monkey.animationSpeed).toBe(0.15);
            expect(monkey.scale.set).toHaveBeenCalledWith(2, 2);
        });
    });

    describe('Animation Properties Preservation', () => {
        it('should preserve position when switching animations', () => {
            monkey.x = 150;
            monkey.y = 350;

            switchMonkeyAnimation('run');

            expect(monkey.x).toBe(150);
            expect(monkey.y).toBe(350);
        });

        it('should preserve scale when switching animations', () => {
            monkey.scale.x = -2; // Flipped direction
            monkey.scale.y = 2;

            switchMonkeyAnimation('jump');

            expect(monkey.scale.set).toHaveBeenCalledWith(-2, 2);
        });

        it('should set loop and play properties correctly', () => {
            switchMonkeyAnimation('run');

            expect(monkey.loop).toBe(true);
            expect(monkey.play).toHaveBeenCalled();
        });
    });

    describe('Animation Speed Configuration', () => {
        it('should use correct speed for idle animation', () => {
            switchMonkeyAnimation('idle');
            expect(monkey.animationSpeed).toBe(0.15);
        });

        it('should use correct speed for run animation', () => {
            switchMonkeyAnimation('run');
            expect(monkey.animationSpeed).toBe(0.2);
        });

        it('should use correct speed for jump animation', () => {
            switchMonkeyAnimation('jump');
            expect(monkey.animationSpeed).toBe(0.25);
        });
    });

    describe('Error Handling', () => {
        it('should handle missing frames gracefully', () => {
            idleFrames = [];
            runFrames = [];
            jumpFrames = [];

            // Should not throw error
            expect(() => switchMonkeyAnimation('idle')).not.toThrow();
            expect(() => switchMonkeyAnimation('run')).not.toThrow();
            expect(() => switchMonkeyAnimation('jump')).not.toThrow();
        });

        it('should handle null monkey gracefully', () => {
            monkey = null;

            // Should not throw error
            expect(() => switchMonkeyAnimation('idle')).not.toThrow();
        });
    });

    describe('Frame Count Validation', () => {
        it('should work with correct frame counts', () => {
            expect(idleFrames.length).toBe(18);
            expect(runFrames.length).toBe(8);
            expect(jumpFrames.length).toBe(4);

            switchMonkeyAnimation('idle');
            switchMonkeyAnimation('run');
            switchMonkeyAnimation('jump');

            // All switches should work without errors
            expect(monkey.play).toHaveBeenCalledTimes(3);
        });
    });
});
