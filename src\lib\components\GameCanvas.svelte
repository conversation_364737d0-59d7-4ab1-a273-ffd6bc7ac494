<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { browser } from '$app/environment';
  import * as PIXI from 'pixi.js';
  import { gameStore, userStore } from '$lib/stores';
  import { updateInputFromKeyboard, createBananaParticles, updateParticles, type Particle } from '$lib/utils/gameUtils';

  // Canvas element reference
  let canvasContainer: HTMLDivElement;
  let pixiApp: PIXI.Application;
  let gameInitialized = false;
  
  // Game objects
  let monkey: PIXI.AnimatedSprite;
  const bananas: PIXI.Sprite[] = [];
  let particles: Particle[] = [];
  let particleContainer: PIXI.Container;

  // Input handling
  const keysPressed = new Set<string>();

  // Game state
  const monkeyVelocity = { x: 0, y: 0 };
  const GRAVITY = 0.5;
  const JUMP_FORCE = -12;
  const MOVE_SPEED = 5;
  const GROUND_Y = 500;
  let monkeyState: 'idle' | 'moving' | 'jumping' = 'idle';

  onMount(async () => {
    if (browser) {
      await initializeGame();
    }
  });

  onDestroy(() => {
    cleanup();
  });

  async function initializeGame() {
    try {
      // Create PixiJS application
      pixiApp = new PIXI.Application();
      await pixiApp.init({
        width: 800,
        height: 600,
        backgroundColor: 0x228B22, // Forest green
        antialias: true
      });

      // Add canvas to container
      // eslint-disable-next-line svelte/no-dom-manipulating
      canvasContainer.appendChild(pixiApp.canvas);

      // Create game objects
      await createGameObjects();
      
      // Set up input handling
      setupInputHandling();
      
      // Start game loop
      pixiApp.ticker.add(gameLoop);
      
      gameInitialized = true;
      // eslint-disable-next-line no-console
      console.log('Game initialized successfully!');
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to initialize game:', error);
    }
  }

  async function createGameObjects() {
    try {
      // Load monkey idle sprite sheet
      // eslint-disable-next-line no-console
      console.log('Loading monkey idle sprite sheet...');
      const idleTexture = await PIXI.Assets.load('/assets/monkey/Idle.png');
      // eslint-disable-next-line no-console
      console.log('Idle texture loaded:', idleTexture.width, 'x', idleTexture.height);

      // Horizontal sprite sheet with 18 frames, each frame is 32x32 pixels
      const totalFrames = 18;
      const frameWidth = 32;
      const frameHeight = 32;

      // eslint-disable-next-line no-console
      console.log('Horizontal sprite sheet - Frame dimensions:', frameWidth, 'x', frameHeight, 'Total frames:', totalFrames);
      console.log('Expected sprite sheet size:', frameWidth * totalFrames, 'x', frameHeight);
      console.log('Actual sprite sheet size:', idleTexture.width, 'x', idleTexture.height);

      // Create frames for the idle animation
      const idleFrames: PIXI.Texture[] = [];

      for (let i = 0; i < totalFrames; i++) {
        const frame = new PIXI.Texture({
          source: idleTexture.source,
          frame: new PIXI.Rectangle(i * frameWidth, 0, frameWidth, frameHeight)
        });
        idleFrames.push(frame);
      }

      // Create animated monkey sprite
      monkey = new PIXI.AnimatedSprite(idleFrames);
      monkey.animationSpeed = 0.15; // Animation speed (frames per tick)
      monkey.loop = true;
      monkey.play();

      // Position the sprite (no scaling needed since 32x32 is a good size)
      monkey.x = 100;
      monkey.y = GROUND_Y - frameHeight;
      pixiApp.stage.addChild(monkey);

      // eslint-disable-next-line no-console
      console.log('Monkey animated sprite created successfully!');
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to load monkey sprite:', error);

      // Fallback to simple rectangle if sprite loading fails
      monkey = new PIXI.AnimatedSprite([PIXI.Texture.WHITE]);
      monkey.width = 32;
      monkey.height = 32;
      monkey.tint = 0x8B4513; // Brown color for monkey
      monkey.x = 100;
      monkey.y = GROUND_Y - 32;
      pixiApp.stage.addChild(monkey);
    }

    // Create particle container
    particleContainer = new PIXI.Container();
    pixiApp.stage.addChild(particleContainer);

    // Create some bananas to collect
    createBananas();
    
    // Create ground (simple rectangle)
    const ground = new PIXI.Sprite(PIXI.Texture.WHITE);
    ground.width = 800;
    ground.height = 20;
    ground.tint = 0x654321; // Brown ground
    ground.x = 0;
    ground.y = GROUND_Y + 12;
    pixiApp.stage.addChild(ground);
  }

  function createBananas() {
    // Create a few bananas to collect
    const bananaPositions = [
      { x: 200, y: 450 },
      { x: 400, y: 350 },
      { x: 600, y: 400 },
      { x: 300, y: 300 }
    ];

    bananaPositions.forEach(pos => {
      const banana = new PIXI.Sprite(PIXI.Texture.WHITE);
      banana.width = 20;
      banana.height = 20;
      banana.tint = 0xFFD700; // Gold color for banana
      banana.x = pos.x;
      banana.y = pos.y;
      bananas.push(banana);
      pixiApp.stage.addChild(banana);
    });
  }

  let canvasFocused = false;

  function setupInputHandling() {
    // Keyboard event listeners - only when canvas is focused
    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);

    // Canvas focus handling
    if (pixiApp.canvas) {
      pixiApp.canvas.tabIndex = 0; // Make canvas focusable
      pixiApp.canvas.addEventListener('focus', () => canvasFocused = true);
      pixiApp.canvas.addEventListener('blur', () => canvasFocused = false);
      pixiApp.canvas.addEventListener('click', () => pixiApp.canvas.focus());
    }
  }

  function handleKeyDown(event: KeyboardEvent) {
    // Only capture game input when canvas is focused and not typing in form fields
    if (!canvasFocused || isTypingInForm(event.target)) {
      return;
    }

    keysPressed.add(event.code);
    event.preventDefault();
  }

  function handleKeyUp(event: KeyboardEvent) {
    // Only capture game input when canvas is focused and not typing in form fields
    if (!canvasFocused || isTypingInForm(event.target)) {
      return;
    }

    keysPressed.delete(event.code);
    event.preventDefault();
  }

  function isTypingInForm(target: EventTarget | null): boolean {
    if (!target || !(target instanceof HTMLElement)) return false;

    const tagName = target.tagName.toLowerCase();
    return tagName === 'input' || tagName === 'textarea' || tagName === 'select' ||
           target.contentEditable === 'true' || target.closest('form') !== null;
  }

  function gameLoop(ticker: PIXI.Ticker) {
    if (!gameInitialized) return;

    // Update input
    const input = updateInputFromKeyboard({
      left: false,
      right: false,
      up: false,
      down: false,
      jump: false,
      interact: false
    }, keysPressed);

    // Update monkey movement
    updateMonkeyMovement(input, ticker.deltaTime);
    
    // Check banana collisions
    checkBananaCollisions();
    
    // Update particles
    updateParticleSystem();
    
    // Update stores
    gameStore.updateMonkeyPosition(monkey.x, monkey.y);
  }

  function updateMonkeyMovement(input: { left: boolean; right: boolean; up: boolean; jump: boolean }, deltaTime: number) {
    const monkeyHeight = 32; // Fixed size since we know it's 32x32
    const isOnGround = monkey.y >= GROUND_Y - monkeyHeight;

    // Horizontal movement
    if (input.left) {
      monkeyVelocity.x = -MOVE_SPEED;
      monkey.scale.x = -1; // Flip sprite to face left
    } else if (input.right) {
      monkeyVelocity.x = MOVE_SPEED;
      monkey.scale.x = 1; // Face right
    } else {
      monkeyVelocity.x *= 0.8; // Friction
    }

    // Jumping
    if (input.jump && isOnGround) {
      monkeyVelocity.y = JUMP_FORCE;
    }

    // Apply gravity
    monkeyVelocity.y += GRAVITY * deltaTime;

    // Update position
    monkey.x += monkeyVelocity.x * deltaTime;
    monkey.y += monkeyVelocity.y * deltaTime;

    // Get monkey dimensions (32x32 pixels)
    const monkeyWidth = 32;

    // Boundary checks
    monkey.x = Math.max(0, Math.min(monkey.x, 800 - monkeyWidth));

    // Ground collision
    const groundLevel = GROUND_Y - monkeyHeight;
    if (monkey.y >= groundLevel) {
      monkey.y = groundLevel;
      monkeyVelocity.y = 0;
    }

    // Update monkey state and animation
    const isMoving = Math.abs(monkeyVelocity.x) > 0.1;
    const newState = !isOnGround ? 'jumping' : isMoving ? 'moving' : 'idle';

    if (newState !== monkeyState) {
      monkeyState = newState;

      // For now, we only have idle animation, so play it when idle
      if (monkeyState === 'idle') {
        if (!monkey.playing) {
          monkey.play();
        }
      } else {
        // When moving or jumping, show a static frame for now
        // Later we can add running/jumping animations
        if (monkey.playing) {
          monkey.stop();
          monkey.gotoAndStop(0); // Show first frame of idle animation
        }
      }
    }

    // Always ensure idle animation is playing when idle
    if (monkeyState === 'idle' && !monkey.playing) {
      monkey.play();
    }
  }

  function checkBananaCollisions() {
    bananas.forEach((banana) => {
      if (banana.visible && 
          monkey.x < banana.x + banana.width &&
          monkey.x + monkey.width > banana.x &&
          monkey.y < banana.y + banana.height &&
          monkey.y + monkey.height > banana.y) {
        
        // Collect banana
        banana.visible = false;
        
        // Add bananas to user store
        userStore.addBananas(5);
        
        // Create particle effect
        const newParticles = createBananaParticles(banana.x + 10, banana.y + 10, 8);
        particles.push(...newParticles);
        
        // eslint-disable-next-line no-console
        console.log('Banana collected! +5 bananas');
      }
    });
  }

  function updateParticleSystem() {
    // Update existing particles
    particles = updateParticles(particles);
    
    // Clear particle container
    particleContainer.removeChildren();
    
    // Render particles
    particles.forEach(particle => {
      const particleSprite = new PIXI.Sprite(PIXI.Texture.WHITE);
      particleSprite.width = 4 * particle.scale;
      particleSprite.height = 4 * particle.scale;
      particleSprite.tint = particle.color;
      particleSprite.x = particle.x;
      particleSprite.y = particle.y;
      particleSprite.alpha = particle.life / particle.maxLife;
      particleContainer.addChild(particleSprite);
    });
  }

  function cleanup() {
    if (pixiApp) {
      pixiApp.destroy(true);
    }

    // Remove event listeners (only if in browser)
    if (browser) {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    }

    gameInitialized = false;
  }
</script>

{#if browser}
  <div class="game-canvas-container">
    <div bind:this={canvasContainer} class="canvas-wrapper" class:focused={canvasFocused}></div>

    <div class="game-controls">
      <p><strong>Controls:</strong></p>
      <p>Arrow Keys or WASD: Move</p>
      <p>Space: Jump</p>
      <p>Collect golden bananas to earn rewards!</p>
      {#if !canvasFocused}
        <p class="focus-hint">💡 Click on the game area to start playing!</p>
      {/if}
    </div>
  </div>
{:else}
  <div class="game-loading">
    <p>🐒 Loading jungle adventure...</p>
  </div>
{/if}

<style>
  .game-canvas-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }
  
  .canvas-wrapper {
    border: 2px solid #10B981;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
    cursor: pointer;
  }

  .canvas-wrapper.focused {
    border-color: #059669;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2), 0 4px 6px rgba(0, 0, 0, 0.1);
  }
  
  .game-controls {
    background: #F3F4F6;
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
    font-size: 0.9rem;
    color: #374151;
  }
  
  .game-controls p {
    margin: 0.25rem 0;
  }
  
  .game-controls strong {
    color: #10B981;
  }

  .focus-hint {
    color: #F59E0B !important;
    font-weight: 500;
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
  }

  .game-loading {
    background: #F3F4F6;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
    color: #6B7280;
    border: 2px dashed #D1D5DB;
  }
</style>
